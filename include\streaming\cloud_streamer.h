
#ifndef CLOUD_STREAMER_H
#define CLOUD_STREAMER_H

#include "../include/common.h"
#include "../include/config/cloud_streamer_config.h"
#include "../include/transport/video_transport_interface.h"
#include <chrono>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <queue>
#include <condition_variable>
#include <gst/gst.h>
#include <gst/sdp/sdp.h>
#include <gst/video/video.h>
#include <gst/webrtc/webrtc.h>
#include <gst/app/gstappsrc.h>
#include <gst/allocators/gstdmabuf.h>


// WebRTC信令处理
class WebRTCSignaling {
private:
    std::string signaling_server_;
    std::string room_id_;
    bool connected_ = false;

public:
    bool init(const std::string& server, const std::string& room);
    bool send_offer(const std::string& sdp);
    bool send_answer(const std::string& sdp);
    bool send_ice_candidate(const std::string& candidate);
    void cleanup();

    // 回调函数
    std::function<void(const std::string&)> on_answer_received;
    std::function<void(const std::string&)> on_ice_candidate_received;
};

class CloudStreamer {
private:
    CloudStreamerConfig config_;
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread streamer_thread_;

    // Transport接口
    video_transport::IVideoSubscriber* video_subscriber_;

    // GStreamer相关
    GstElement* pipeline_ = nullptr;
    GstElement* appsrc_ = nullptr;
    GMainLoop* main_loop_ = nullptr;
    std::thread gst_thread_;

    // WebRTC相关
    std::unique_ptr<WebRTCSignaling> webrtc_signaling_;
    GstElement* webrtcbin_ = nullptr;

    // 统计信息
    std::atomic<uint64_t> frames_sent_{0};
    std::atomic<uint64_t> frames_dropped_{0};
    std::atomic<uint64_t> bytes_sent_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;

    // 自适应码率控制
    std::atomic<uint32_t> current_bitrate_{0};
    std::chrono::steady_clock::time_point last_bitrate_adjust_;
    uint32_t consecutive_drops_{0};

    // 错误处理
    std::atomic<uint32_t> pipeline_errors_{0};
    std::chrono::steady_clock::time_point last_error_time_;

    // 动态格式检测和关键帧等待
    std::atomic<bool> pipeline_initialized_{false};
    std::atomic<bool> waiting_for_keyframe_{false};
    InputFormat current_input_format_{FORMAT_UNKNOWN};
    uint32_t current_v4l2_format_{0};
    uint32_t current_width_{0};
    uint32_t current_height_{0};

public:
    CloudStreamer(const CloudStreamerConfig& config): config_(config) {
        current_bitrate_.store(config_.bitrate);
        last_bitrate_adjust_ = std::chrono::steady_clock::now();
        last_error_time_ = std::chrono::steady_clock::now();
    };
    ~CloudStreamer() { stop(); }

    bool init(video_transport::IVideoSubscriber* subscriber) {
        if (running_.load()) {
            LOG_W("Cloud streamer already running");
            return true;
        }

        // 初始化GStreamer
        if (!gst_is_initialized()) {
            gst_init(nullptr, nullptr);
        }

        // 设置视频订阅者
        video_subscriber_ = subscriber;

        // 重置状态变量
        pipeline_initialized_.store(false);
        waiting_for_keyframe_.store(false);
        current_input_format_ = FORMAT_UNKNOWN;
        current_v4l2_format_ = 0;
        current_width_ = 0;
        current_height_ = 0;

        LOG_I("Cloud streamer initialized for %s streaming to %s (dynamic format detection enabled)",
              (config_.type == CloudStreamerConfig::WEBRTC) ? "WebRTC" : "RTMP",
              config_.url.c_str());
        return true;
    }

    void start() {
        if (running_.load()) {
            LOG_W("Cloud streamer already running");
            return;
        }

        stop_requested_.store(false);

        // 启动GStreamer主循环
        main_loop_ = g_main_loop_new(nullptr, FALSE);
        gst_thread_ = std::thread([this] {
            g_main_loop_run(main_loop_);
        });

        // 启动流处理线程
        streamer_thread_ = std::thread(&CloudStreamer::run, this);
        running_.store(true);

        // 启动管道
        if (pipeline_) {
            gst_element_set_state(pipeline_, GST_STATE_PLAYING);
        }

        LOG_I("Cloud streamer started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);

        // 停止管道
        if (pipeline_) {
            gst_element_set_state(pipeline_, GST_STATE_NULL);
        }

        // 停止主循环
        if (main_loop_) {
            g_main_loop_quit(main_loop_);
        }

        // 等待线程结束
        if (streamer_thread_.joinable()) {
            streamer_thread_.join();
        }

        if (gst_thread_.joinable()) {
            gst_thread_.join();
        }

        running_.store(false);
        cleanup();

        LOG_I("Cloud streamer stopped");
    }

    void run() {
        LOG_I("Cloud streamer thread started");

        while (!stop_requested_.load()) {
            try {
                video_transport::IVideoSubscriber::SubscriberData input_frame;
                // 从队列中获取帧 100ms超时
                if (video_subscriber_->receive_frame_buffer(input_frame, 100) != video_transport::BufferResult::SUCCESS) {
                    continue;
                }

                if (push_frame(input_frame)) {
                    frames_sent_.fetch_add(1);
                    bytes_sent_.fetch_add(input_frame.meta.data_size);
                    consecutive_drops_ = 0; // 重置连续丢帧计数
                } else {
                    frames_dropped_.fetch_add(1);
                    consecutive_drops_++;

                    // 自适应码率调整
                    if (config_.adaptive_bitrate) {
                        adjust_bitrate_on_drop();
                    }
                }
            } catch (const std::exception& e) {
                LOG_E("Cloud streamer loop exception: %s", e.what());
                frames_dropped_.fetch_add(1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Cloud streamer thread stopped");
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_sent;
        uint64_t frames_dropped;
        uint64_t bytes_sent;
        float bitrate_kbps;
        float fps;
        float cpu_usage;
    };

    void get_stats(Stats& stats) {
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_bytes = 0;
        static uint64_t last_frames = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        uint64_t current_bytes = bytes_sent_.load();
        uint64_t current_frames = frames_sent_.load();

        float bitrate_kbps = 0.0f;
        float fps = 0.0f;

        if (elapsed > 0) {
            bitrate_kbps = (float)(current_bytes - last_bytes) * 8 / (elapsed * 1000);
            fps = (float)(current_frames - last_frames) / elapsed;
        }

        last_time = now;
        last_bytes = current_bytes;
        last_frames = current_frames;

        stats.frames_sent = current_frames;
        stats.frames_dropped = frames_dropped_.load();
        stats.bytes_sent = current_bytes;
        stats.bitrate_kbps = bitrate_kbps;
        stats.fps = fps;
        stats.cpu_usage = cpu_monitor_.get_usage();
    }

private:
    bool push_frame(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        if (!frame.meta.is_valid) {
            LOG_W("Invalid frame metadata");
            return false;
        }

        if (frame.data_ptr == nullptr || frame.meta.data_size == 0) {
            LOG_W("Frame data is empty or invalid size: %u", frame.meta.data_size);
            return false;
        }

        // 检测输入格式变化，如果需要则重新初始化pipeline
        if (!check_and_update_pipeline_format(frame)) {
            LOG_E("Failed to update pipeline format");
            return false;
        }

        // 对于编码格式，检查是否需要等待关键帧
        if (is_input_encoded(frame.meta.format)) {
            if (waiting_for_keyframe_.load()) {
                if (!frame.meta.is_keyframe) {
                    LOG_D("Waiting for keyframe, dropping frame %lu", frame.meta.frame_id);
                    return true; // 跳过非关键帧
                } else {
                    LOG_I("Received keyframe, starting stream transmission");
                    waiting_for_keyframe_.store(false);
                }
            }

            // 验证编码数据格式
            if (current_input_format_ == FORMAT_H264) {
                if (!validate_h264_data(static_cast<const uint8_t*>(frame.data_ptr), frame.meta.data_size)) {
                    LOG_W("Invalid H.264 data format detected, frame_id: %lu", frame.meta.frame_id);
                    return true; // 跳过无效帧
                }
            }
        }

        if (!appsrc_) {
            LOG_E("appsrc not initialized");
            return false;
        }

        // 创建GStreamer缓冲区
        GstBuffer* gst_buf = create_memory_buffer(frame);
        if (!gst_buf) {
            LOG_E("Failed to create GStreamer buffer");
            return false;
        }

        // 设置时间戳和元数据
        set_buffer_metadata(gst_buf, frame);

        // 推送到GStreamer
        GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc_), gst_buf);
        if (ret != GST_FLOW_OK) {
            LOG_E("Failed to push buffer to appsrc: %s (%d)",
                  gst_flow_get_name(ret), ret);

            // 对于特定的错误类型，提供更详细的信息
            if (ret == GST_FLOW_FLUSHING) {
                LOG_W("Pipeline is flushing, this is normal during shutdown");
            } else if (ret == GST_FLOW_EOS) {
                LOG_W("Pipeline reached end of stream");
            } else if (ret == GST_FLOW_NOT_LINKED) {
                LOG_E("Pipeline elements are not properly linked");
            }

            return false;
        }

        return true;
    }

    // 创建DMA缓冲区（零拷贝）
    GstBuffer* create_dma_buffer(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        // 创建DMA内存
        GstAllocator* allocator = gst_dmabuf_allocator_new();
        if (!allocator) {
            LOG_E("Failed to create DMA allocator");
            return nullptr;
        }

        GstMemory* memory = gst_dmabuf_allocator_alloc(allocator, frame.fd, frame.meta.data_size);
        if (!memory) {
            LOG_E("Failed to allocate DMA memory");
            gst_object_unref(allocator);
            return nullptr;
        }

        GstBuffer* buffer = gst_buffer_new();
        gst_buffer_append_memory(buffer, memory);
        gst_object_unref(allocator);

        return buffer;
    }

    // 创建常规内存缓冲区
    GstBuffer* create_memory_buffer(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        GstBuffer* gst_buf = gst_buffer_new_allocate(nullptr, frame.meta.data_size, nullptr);
        if (!gst_buf) {
            LOG_E("Failed to allocate GStreamer buffer");
            return nullptr;
        }

        GstMapInfo map;
        if (!gst_buffer_map(gst_buf, &map, GST_MAP_WRITE)) {
            LOG_E("Failed to map GStreamer buffer");
            gst_buffer_unref(gst_buf);
            return nullptr;
        }

        memcpy(map.data, frame.data_ptr, frame.meta.data_size);
        gst_buffer_unmap(gst_buf, &map);

        return gst_buf;
    }

    // 设置缓冲区元数据
    void set_buffer_metadata(GstBuffer* buffer, const video_transport::IVideoSubscriber::SubscriberData& frame) {
        // 设置时间戳
        GST_BUFFER_PTS(buffer) = frame.meta.timestamp * 1000; // us -> ns
        GST_BUFFER_DTS(buffer) = GST_BUFFER_PTS(buffer);

        // 设置关键帧标志
        if (frame.meta.is_keyframe) {
            GST_BUFFER_FLAG_UNSET(buffer, GST_BUFFER_FLAG_DELTA_UNIT);
        } else {
            GST_BUFFER_FLAG_SET(buffer, GST_BUFFER_FLAG_DELTA_UNIT);
        }

        // 设置帧序号
        GST_BUFFER_OFFSET(buffer) = frame.meta.frame_id;
        GST_BUFFER_OFFSET_END(buffer) = frame.meta.frame_id + 1;
    }
    


    void cleanup() {
        // 清理WebRTC信令
        webrtc_signaling_.reset();

        // 清理GStreamer资源
        if (pipeline_) {
            gst_object_unref(pipeline_);
            pipeline_ = nullptr;
        }

        if (main_loop_) {
            g_main_loop_unref(main_loop_);
            main_loop_ = nullptr;
        }

        appsrc_ = nullptr;
        webrtcbin_ = nullptr;
    }



    // 添加编码器元素到管道
    void add_encoder_elements(std::ostringstream& pipeline) {
        if (config_.use_hw_encoder && check_hardware_encoder_support()) {
            // 硬件编码器
            pipeline << "! mpph264enc "
                     << "bps=" << config_.bitrate << " "
                     << "bps-min=" << config_.min_bitrate << " "
                     << "bps-max=" << config_.max_bitrate << " "
                     << "gop=" << config_.gop_size << " "
                     << "profile=" << get_h264_profile_value(config_.profile) << " "
                     << "rc-mode=" << (config_.adaptive_bitrate ? "1" : "0") << " "
                     << "! h264parse ";
            LOG_I("Using hardware encoder (mpph264enc)");
        } else {
            // 软件编码器
            pipeline << "! x264enc "
                     << "bitrate=" << (config_.bitrate / 1000) << " "  // kbps
                     << "key-int-max=" << config_.gop_size << " "
                     << "tune=" << config_.tune << " "
                     << "speed-preset=" << config_.preset << " "
                     << "profile=" << config_.profile << " "
                     << "threads=4 "
                     << "! h264parse ";
            LOG_I("Using software encoder (x264enc)");
        }
    }

    // 检查硬件编码器支持
    bool check_hardware_encoder_support() {
        GstElementFactory* factory = gst_element_factory_find("mpph264enc");
        if (factory) {
            gst_object_unref(factory);
            return true;
        }
        LOG_W("Hardware encoder (mpph264enc) not available, falling back to software");
        return false;
    }

    // 获取H264 profile数值
    int get_h264_profile_value(const std::string& profile) {
        if (profile == "baseline") return 0;
        if (profile == "main") return 1;
        if (profile == "high") return 2;
        return 0; // 默认baseline
    }

    // 检测输入格式类型
    enum InputFormat {
        FORMAT_UNKNOWN = 0,
        FORMAT_YUV420,      // I420/YUV420P
        FORMAT_NV12,        // NV12
        FORMAT_YUYV,        // YUY2
        FORMAT_MJPEG,       // Motion JPEG
        FORMAT_H264,        // H.264 encoded
        FORMAT_H265         // H.265 encoded
    };

    // 从V4L2格式转换为内部格式
    InputFormat v4l2_format_to_input_format(uint32_t v4l2_format) {
        switch (v4l2_format) {
            case V4L2_PIX_FMT_H264:
                return FORMAT_H264;
            case V4L2_PIX_FMT_HEVC:
                return FORMAT_H265;
            case V4L2_PIX_FMT_MJPEG:
                return FORMAT_MJPEG;
            case V4L2_PIX_FMT_YUYV:
                return FORMAT_YUYV;
            case V4L2_PIX_FMT_NV12:
                return FORMAT_NV12;
            case V4L2_PIX_FMT_YUV420:
                return FORMAT_YUV420;
            default:
                LOG_W("Unknown V4L2 format: 0x%x, assuming YUV420", v4l2_format);
                return FORMAT_YUV420;
        }
    }

    // 检测输入是否已编码
    bool is_input_encoded(uint32_t v4l2_format) {
        InputFormat format = v4l2_format_to_input_format(v4l2_format);
        return (format == FORMAT_H264 || format == FORMAT_H265);
    }

    // 检测输入格式（从实际帧数据中获取）
    InputFormat detect_input_format_from_frame(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        return v4l2_format_to_input_format(frame.meta.format);
    }

    // 检查并更新pipeline格式
    bool check_and_update_pipeline_format(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        // 检查格式是否发生变化
        bool format_changed = (current_v4l2_format_ != frame.meta.format) ||
                              (current_width_ != frame.meta.width) ||
                              (current_height_ != frame.meta.height);

        if (!pipeline_initialized_.load() || format_changed) {
            LOG_I("Format change detected or first initialization: format=0x%x, %dx%d",
                  frame.meta.format, frame.meta.width, frame.meta.height);

            // 更新当前格式信息
            current_v4l2_format_ = frame.meta.format;
            current_input_format_ = detect_input_format_from_frame(frame);
            current_width_ = frame.meta.width;
            current_height_ = frame.meta.height;

            // 对于编码格式，需要等待关键帧
            if (is_input_encoded(frame.meta.format)) {
                waiting_for_keyframe_.store(true);
                LOG_I("Encoded format detected, waiting for keyframe");
            } else {
                waiting_for_keyframe_.store(false);
            }

            // 重新初始化pipeline
            if (!reinitialize_pipeline_with_format(frame)) {
                LOG_E("Failed to reinitialize pipeline with new format");
                return false;
            }

            pipeline_initialized_.store(true);
        }

        return true;
    }

    // 根据检测到的格式重新初始化pipeline
    bool reinitialize_pipeline_with_format(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        // 停止当前pipeline
        if (pipeline_) {
            gst_element_set_state(pipeline_, GST_STATE_NULL);
            gst_object_unref(pipeline_);
            pipeline_ = nullptr;
            appsrc_ = nullptr;
        }

        // 根据格式创建新的pipeline
        std::string pipeline_desc = build_dynamic_pipeline_string(frame);
        if (pipeline_desc.empty()) {
            LOG_E("Failed to build pipeline string for format 0x%x", frame.meta.format);
            return false;
        }

        LOG_I("Creating pipeline: %s", pipeline_desc.c_str());

        GError* error = nullptr;
        pipeline_ = gst_parse_launch(pipeline_desc.c_str(), &error);

        if (!pipeline_ || error) {
            LOG_E("Failed to create pipeline: %s", error ? error->message : "unknown error");
            if (error) g_error_free(error);
            return false;
        }

        // 配置pipeline元素
        if (!configure_pipeline_elements_with_format(frame)) {
            LOG_E("Failed to configure pipeline elements");
            return false;
        }

        // 设置管道总线监听
        setup_pipeline_bus();

        // 启动pipeline
        GstStateChangeReturn ret = gst_element_set_state(pipeline_, GST_STATE_PLAYING);
        if (ret == GST_STATE_CHANGE_FAILURE) {
            LOG_E("Failed to start pipeline");
            return false;
        }

        LOG_I("Pipeline reinitialized successfully for format 0x%x (%dx%d)",
              frame.meta.format, frame.meta.width, frame.meta.height);
        return true;
    }

    // 根据检测到的格式动态构建pipeline字符串
    std::string build_dynamic_pipeline_string(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        std::ostringstream pipeline;
        InputFormat format = detect_input_format_from_frame(frame);

        // 输入源 - 优化低延时设置
        if (config_.low_latency_mode) {
            pipeline << "appsrc name=source ! queue max-size-buffers=3 leaky=downstream ";
        } else {
            pipeline << "appsrc name=source ! queue max-size-buffers=10 leaky=downstream ";
        }

        // 根据检测到的格式添加相应的处理
        switch (format) {
            case FORMAT_H264:
                // H.264编码数据，直接解析
                pipeline << "! h264parse config-interval=1 "
                         << "disable-passthrough=true "
                         << "split-packetized=true ";
                break;
            case FORMAT_H265:
                // H.265编码数据，直接解析
                pipeline << "! h265parse config-interval=1 "
                         << "disable-passthrough=true ";
                break;
            case FORMAT_MJPEG:
                // MJPEG需要解码后重新编码
                pipeline << "! mppjpegdec ! videoconvert ";
                add_encoder_elements(pipeline);
                break;
            case FORMAT_YUYV:
            case FORMAT_NV12:
            case FORMAT_YUV420:
            default:
                // 原始YUV数据，需要编码
                add_encoder_elements(pipeline);
                break;
        }

        // 根据流类型添加输出
        if (config_.type == CloudStreamerConfig::WEBRTC) {
            // WebRTC传输
            pipeline << "! rtph264pay config-interval=1 pt=96 mtu=1400 "
                     << "! webrtcbin bundle-policy=max-bundle name=webrtcbin "
                     << "stun-server=stun://stun.l.google.com:19302";
        } else {
            // RTMP传输
            pipeline << "! flvmux streamable=true name=mux ";

            // 验证并构建RTMP URL
            std::string rtmp_url = validate_and_build_rtmp_url(config_.url);
            if (rtmp_url.empty()) {
                LOG_E("Invalid RTMP URL: %s", config_.url.c_str());
                return "";
            }

            pipeline << "! rtmpsink location=\"" << rtmp_url << "\" "
                     << "sync=" << (config_.low_latency_mode ? "false" : "true") << " "
                     << "async=false max-lateness=-1";
        }

        LOG_D("Dynamic pipeline: %s", pipeline.str().c_str());
        return pipeline.str();
    }

    // 根据格式配置pipeline元素
    bool configure_pipeline_elements_with_format(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        // 获取appsrc元素
        appsrc_ = gst_bin_get_by_name(GST_BIN(pipeline_), "source");
        if (!appsrc_) {
            LOG_E("Failed to get appsrc element");
            return false;
        }

        // 配置appsrc基本属性 - 优化低延时
        guint64 max_bytes;
        if (config_.low_latency_mode) {
            max_bytes = (guint64)(config_.bitrate / 8 * 0.5); // 0.5秒缓冲
        } else {
            max_bytes = (guint64)(config_.bitrate / 8 * 2); // 2秒缓冲
        }

        g_object_set(appsrc_,
            "stream-type", GST_APP_STREAM_TYPE_STREAM,
            "format", GST_FORMAT_TIME,
            "is-live", TRUE,
            "max-bytes", max_bytes,
            "block", FALSE,
            "do-timestamp", config_.low_latency_mode ? FALSE : TRUE,
            nullptr);

        // 根据实际格式配置caps
        GstCaps* caps = create_caps_for_format(frame);
        if (!caps) {
            LOG_E("Failed to create caps for format 0x%x", frame.meta.format);
            return false;
        }

        g_object_set(appsrc_, "caps", caps, nullptr);
        gst_caps_unref(caps);

        LOG_I("Configured appsrc for format 0x%x (%dx%d)",
              frame.meta.format, frame.meta.width, frame.meta.height);
        return true;
    }

    // 为特定格式创建caps
    GstCaps* create_caps_for_format(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        InputFormat format = detect_input_format_from_frame(frame);
        GstCaps* caps = nullptr;

        switch (format) {
            case FORMAT_H264:
                caps = gst_caps_new_simple("video/x-h264",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created H.264 caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
            case FORMAT_H265:
                caps = gst_caps_new_simple("video/x-h265",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created H.265 caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
            case FORMAT_MJPEG:
                caps = gst_caps_new_simple("image/jpeg",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created MJPEG caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
            case FORMAT_YUYV:
                caps = gst_caps_new_simple("video/x-raw",
                    "format", G_TYPE_STRING, "YUY2",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created YUYV caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
            case FORMAT_NV12:
                caps = gst_caps_new_simple("video/x-raw",
                    "format", G_TYPE_STRING, "NV12",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created NV12 caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
            case FORMAT_YUV420:
            default:
                caps = gst_caps_new_simple("video/x-raw",
                    "format", G_TYPE_STRING, "I420",
                    "width", G_TYPE_INT, (int)frame.meta.width,
                    "height", G_TYPE_INT, (int)frame.meta.height,
                    "framerate", GST_TYPE_FRACTION, config_.fps, 1,
                    nullptr);
                LOG_D("Created I420 caps: %dx%d@%dfps", frame.meta.width, frame.meta.height, config_.fps);
                break;
        }

        return caps;
    }

    // 验证H.264数据格式
    bool validate_h264_data(const uint8_t* data, size_t size) {
        if (!data || size < 4) {
            return false;
        }

        // 检查NAL单元起始码 (0x00000001 或 0x000001)
        if (size >= 4 && data[0] == 0x00 && data[1] == 0x00 && data[2] == 0x00 && data[3] == 0x01) {
            return true; // 4字节起始码
        }
        if (size >= 3 && data[0] == 0x00 && data[1] == 0x00 && data[2] == 0x01) {
            return true; // 3字节起始码
        }

        // 检查Annex-B格式的其他可能性
        for (size_t i = 0; i < size - 3; i++) {
            if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x01) {
                return true;
            }
        }

        return false;
    }

    // 检测H.264流格式
    std::string detect_h264_stream_format(const uint8_t* data, size_t size) {
        if (!data || size < 4) {
            return "byte-stream"; // 默认
        }

        // 检查是否是Annex-B格式 (byte-stream)
        if (validate_h264_data(data, size)) {
            return "byte-stream";
        }

        // 检查是否是AVCC格式 (avc)
        // AVCC格式前4字节是长度，不是起始码
        uint32_t length = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
        if (length > 0 && length < size - 4) {
            return "avc";
        }

        return "byte-stream"; // 默认假设
    }





    // 自适应码率调整
    void adjust_bitrate_on_drop() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_bitrate_adjust_).count();

        // 每5秒最多调整一次
        if (elapsed < 5) return;

        // 连续丢帧超过阈值时降低码率
        if (consecutive_drops_ > 10) {
            uint32_t current = current_bitrate_.load();
            uint32_t new_bitrate = std::max((uint32_t)config_.min_bitrate,
                                          (uint32_t)(current * 0.8)); // 降低20%

            if (new_bitrate != current) {
                current_bitrate_.store(new_bitrate);
                update_encoder_bitrate(new_bitrate);
                last_bitrate_adjust_ = now;
                LOG_I("Adaptive bitrate: reduced to %u bps due to frame drops", new_bitrate);
            }
        }
    }

    // 更新编码器码率
    void update_encoder_bitrate(uint32_t new_bitrate) {
        if (!pipeline_) return;

        // 查找编码器元素
        GstElement* encoder = nullptr;
        if (config_.use_hw_encoder) {
            encoder = gst_bin_get_by_name(GST_BIN(pipeline_), "mpph264enc");
        } else {
            encoder = gst_bin_get_by_name(GST_BIN(pipeline_), "x264enc");
        }

        if (encoder) {
            if (config_.use_hw_encoder) {
                g_object_set(encoder, "bps", new_bitrate, nullptr);
            } else {
                g_object_set(encoder, "bitrate", new_bitrate / 1000, nullptr); // kbps
            }
            gst_object_unref(encoder);
        }
    }

    // 管道错误处理
    static gboolean bus_callback(GstBus* bus, GstMessage* message, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);

        switch (GST_MESSAGE_TYPE(message)) {
            case GST_MESSAGE_ERROR: {
                GError* error = nullptr;
                gchar* debug = nullptr;
                gst_message_parse_error(message, &error, &debug);

                LOG_E("GStreamer error: %s", error ? error->message : "Unknown error");
                if (debug) {
                    LOG_E("Debug info: %s", debug);
                }

                // 检查是否是解析相关错误
                if (error && error->message) {
                    std::string error_msg(error->message);

                    // H.264解析错误
                    if (error_msg.find("Failed to parse stream") != std::string::npos ||
                        error_msg.find("gst_base_parse_check_sync") != std::string::npos) {
                        LOG_E("H.264 stream parsing failed - possible format mismatch");
                        LOG_I("Suggestion: Check if input stream format matches expected H.264 format");

                        // 尝试重置解析器状态
                        if (appsrc_) {
                            LOG_I("Attempting to flush appsrc to recover from parsing error");
                            gst_app_src_end_of_stream(GST_APP_SRC(appsrc_));
                        }
                    }
                    // RTMP相关错误
                    else if (error_msg.find("401") != std::string::npos ||
                        error_msg.find("Unauthorized") != std::string::npos) {
                        LOG_E("RTMP Authentication failed - check username/password");
                    } else if (error_msg.find("404") != std::string::npos ||
                               error_msg.find("Not Found") != std::string::npos) {
                        LOG_E("RTMP Stream not found - check URL and stream key");
                    } else if (error_msg.find("Connection") != std::string::npos) {
                        LOG_E("RTMP Connection error - check network and server");
                    }
                }

                self->handle_pipeline_error();

                if (error) g_error_free(error);
                if (debug) g_free(debug);
                break;
            }
            case GST_MESSAGE_WARNING: {
                GError* warning = nullptr;
                gchar* debug = nullptr;
                gst_message_parse_warning(message, &warning, &debug);

                LOG_W("GStreamer warning: %s", warning ? warning->message : "Unknown warning");
                if (debug) {
                    LOG_W("Debug info: %s", debug);
                }

                if (warning) g_error_free(warning);
                if (debug) g_free(debug);
                break;
            }
            case GST_MESSAGE_EOS:
                LOG_I("End of stream received");
                break;
            default:
                break;
        }

        return TRUE;
    }

    // 验证并构建RTMP URL（支持用户名密码）
    std::string validate_and_build_rtmp_url(const std::string& url) {
        if (url.empty()) {
            LOG_E("RTMP URL is empty");
            return "";
        }

        // 检查URL格式
        if (url.find("rtmp://") != 0) {
            LOG_E("Invalid RTMP URL format: %s", url.c_str());
            return "";
        }

        // URL已经包含认证信息，直接返回
        // 格式: rtmp://username:password@server:port/app/stream
        LOG_I("Using RTMP URL with authentication");
        return url;
    }

    // 处理管道错误
    void handle_pipeline_error() {
        pipeline_errors_.fetch_add(1);
        last_error_time_ = std::chrono::steady_clock::now();

        // 检查是否是RTMP认证错误
        check_rtmp_connection_status();

        // 如果错误频繁，可以考虑重启管道
        if (pipeline_errors_.load() > 5) {
            LOG_E("Too many pipeline errors (%d), consider restarting",
                  pipeline_errors_.load());

            // 可以在这里添加自动重连逻辑
            if (should_auto_reconnect()) {
                schedule_pipeline_restart();
            }
        }
    }

    // 检查RTMP连接状态
    void check_rtmp_connection_status() {
        // 检查rtmpsink元素的状态
        if (pipeline_) {
            GstElement* rtmpsink = gst_bin_get_by_name(GST_BIN(pipeline_), "rtmpsink");
            if (rtmpsink) {
                // 可以通过查询元素状态来判断连接情况
                GstState state;
                gst_element_get_state(rtmpsink, &state, nullptr, 0);

                if (state != GST_STATE_PLAYING) {
                    LOG_W("RTMP sink not in playing state, possible connection issue");
                }

                gst_object_unref(rtmpsink);
            }
        }
    }

    // 判断是否应该自动重连
    bool should_auto_reconnect() {
        auto now = std::chrono::steady_clock::now();
        auto time_since_last_error = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_error_time_).count();

        // 如果错误间隔太短，不进行自动重连
        return time_since_last_error > 30; // 30秒间隔
    }

    // 安排管道重启
    void schedule_pipeline_restart() {
        LOG_I("Scheduling pipeline restart due to frequent errors");
        // 这里可以设置一个标志，在主循环中处理重启
        // 或者启动一个异步任务来处理重启
    }

    // 设置管道总线监听
    void setup_pipeline_bus() {
        if (!pipeline_) return;

        GstBus* bus = gst_element_get_bus(pipeline_);
        if (bus) {
            gst_bus_add_watch(bus, bus_callback, this);
            gst_object_unref(bus);
        }
    }

    // WebRTC回调函数
    static void on_negotiation_needed(GstElement* webrtc, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);
        LOG_I("WebRTC negotiation needed");

        // 创建offer
        GstPromise* promise = gst_promise_new_with_change_func(on_offer_created, user_data, nullptr);
        g_signal_emit_by_name(webrtc, "create-offer", nullptr, promise);
    }

    static void on_offer_created(GstPromise* promise, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);

        const GstStructure* reply = gst_promise_get_reply(promise);
        GstWebRTCSessionDescription* offer = nullptr;
        gst_structure_get(reply, "offer", GST_TYPE_WEBRTC_SESSION_DESCRIPTION, &offer, nullptr);

        // 设置本地描述
        GstPromise* local_promise = gst_promise_new();
        g_signal_emit_by_name(self->webrtcbin_, "set-local-description", offer, local_promise);
        gst_promise_unref(local_promise);

        // 发送offer到信令服务器
        if (self->webrtc_signaling_) {
            gchar* sdp_text = gst_sdp_message_as_text(offer->sdp);
            self->webrtc_signaling_->send_offer(sdp_text);
            g_free(sdp_text);
        }

        gst_webrtc_session_description_free(offer);
        gst_promise_unref(promise);
    }

    static void on_ice_candidate(GstElement* webrtc, guint mline_index,
                                gchar* candidate, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);

        LOG_D("ICE candidate: %s", candidate);

        if (self->webrtc_signaling_) {
            self->webrtc_signaling_->send_ice_candidate(candidate);
        }
    }
};

// WebRTC信令实现（简化版）
bool WebRTCSignaling::init(const std::string& server, const std::string& room) {
    signaling_server_ = server;
    room_id_ = room;

    // 这里应该实现WebSocket连接到信令服务器
    // 为了简化，我们只是记录日志
    LOG_I("WebRTC signaling initialized (simplified implementation)");
    LOG_I("Server: %s, Room: %s", server.c_str(), room.c_str());

    connected_ = true;
    return true;
}

bool WebRTCSignaling::send_offer(const std::string& sdp) {
    if (!connected_) return false;

    LOG_I("Sending WebRTC offer (simplified)");
    LOG_D("SDP: %s", sdp.c_str());

    // 实际实现应该通过WebSocket发送到信令服务器
    return true;
}

bool WebRTCSignaling::send_answer(const std::string& sdp) {
    if (!connected_) return false;

    LOG_I("Sending WebRTC answer (simplified)");
    return true;
}

bool WebRTCSignaling::send_ice_candidate(const std::string& candidate) {
    if (!connected_) return false;

    LOG_D("Sending ICE candidate: %s", candidate.c_str());
    return true;
}

void WebRTCSignaling::cleanup() {
    connected_ = false;
    LOG_I("WebRTC signaling cleanup");
}

#endif // CLOUD_STREAMER_H
