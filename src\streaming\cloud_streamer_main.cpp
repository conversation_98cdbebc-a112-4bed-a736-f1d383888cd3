#include "../../include/common.h"
#include "../../include/config/cloud_streamer_config.h"
#include "../../include/transport/video_transport_interface.h"
#include "../../include/streaming/cloud_streamer.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <json/json.h>
#include <gst/gst.h>

// 全局变量
std::unique_ptr<CloudStreamer> g_cloud_streamer;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_cloud_streamer) {
                g_cloud_streamer->stop();
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: /opt/video_service/config/main_cloud_streamer.json)\n"
              << "  -t, --type TYPE       Stream type (webrtc|rtmp)\n"
              << "  -u, --url URL         Stream URL or signaling server\n"
              << "  --topic TOPIC         DDS topic name (default: video_frames)\n"
              << "  --transport TYPE      Transport type (FASTDDS, DMA, SHMEM) (default: DMA)\n"
              << "  -b, --bitrate RATE    Bitrate in bps (default: 2000000)\n"
              << "  -g, --gop-size SIZE   GOP size (default: 15)\n"
              << "  --hw-encoder          Use hardware encoder (default)\n"
              << "  --sw-encoder          Use software encoder\n"
              << "  -v, --verbose         Enable verbose logging and GStreamer debug output\n"
              << "  --help                Show this help message\n";
}

bool load_config_from_json(const std::string& config_file, CloudStreamerConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        LOG_E("Failed to open config file: %s", config_file.c_str());
        return false;
    }

    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    if (!Json::parseFromStream(builder, file, &root, &errors)) {
        LOG_E("Failed to parse JSON config: %s", errors.c_str());
        return false;
    }

    // 解析配置
    if (root.isMember("type")) {
        std::string type_str = root["type"].asString();
        if (type_str == "webrtc") {
            config.type = CloudStreamerConfig::WEBRTC;
        } else if (type_str == "rtmp") {
            config.type = CloudStreamerConfig::RTMP;
        }
    }
    if (root.isMember("url")) {
        config.url = root["url"].asString();
    }
    if (root.isMember("bitrate")) {
        config.bitrate = root["bitrate"].asInt();
    }
    if (root.isMember("gop_size")) {
        config.gop_size = root["gop_size"].asInt();
    }
    if (root.isMember("use_hw_encoder")) {
        config.use_hw_encoder = root["use_hw_encoder"].asBool();
    }
    if (root.isMember("width")) {
        config.width = root["width"].asInt();
    }
    if (root.isMember("height")) {
        config.height = root["height"].asInt();
    }
    if (root.isMember("fps")) {
        config.fps = root["fps"].asInt();
    }
    if (root.isMember("codec")) {
        config.codec = root["codec"].asString();
    }
    if (root.isMember("preset")) {
        config.preset = root["preset"].asString();
    }
    if (root.isMember("adaptive_bitrate")) {
        config.adaptive_bitrate = root["adaptive_bitrate"].asBool();
    }
    if (root.isMember("min_bitrate")) {
        config.min_bitrate = root["min_bitrate"].asInt();
    }
    if (root.isMember("max_bitrate")) {
        config.max_bitrate = root["max_bitrate"].asInt();
    }
    if (root.isMember("transport_type")) {
        config.transport_type = root["transport_type"].asString();
    }
    if (root.isMember("topic_name")) {
        config.topic_name = root["topic_name"].asString();
    }
    if (root.isMember("domain_id")) {
        config.domain_id = root["domain_id"].asInt();
    }
    if (root.isMember("max_samples")) {
        config.max_samples = root["max_samples"].asInt();
    }
    if (root.isMember("low_latency_mode")) {
        config.low_latency_mode = root["low_latency_mode"].asBool();
    }
    if (root.isMember("max_queue_size")) {
        config.max_queue_size = root["max_queue_size"].asInt();
    }

    return true;
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LEVEL_INFO);
    
    // 默认配置
    CloudStreamerConfig config;
    std::string config_file;

    config.type = CloudStreamerConfig::RTMP;
    config.url = "";

    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"topic", required_argument, 0, 't'},
        {"type", required_argument, 0, 'T'},
        {"url", required_argument, 0, 'u'},
        {"bitrate", required_argument, 0, 'b'},
        {"gop-size", required_argument, 0, 'g'},
        {"hw-encoder", no_argument, 0, 1},
        {"sw-encoder", no_argument, 0, 2},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 3},
        {"transport", required_argument, 0, 4},
        {0, 0, 0, 0}
    };

    load_config_from_json("/opt/video_service/config/main_cloud_streamer.json", config);
    // 第一次解析：只获取配置文件路径
    int c;
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:t:u:T:b:g:v", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!config_file.empty()) {
        if (!load_config_from_json(config_file, config)) {
            LOG_E("Failed to load configuration file");
            return 1;
        }
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:t:u:T:b:g:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 'T':
                if (strcmp(optarg, "webrtc") == 0) {
                    config.type = CloudStreamerConfig::WEBRTC;
                } else if (strcmp(optarg, "rtmp") == 0) {
                    config.type = CloudStreamerConfig::RTMP;
                } else {
                    std::cerr << "Invalid stream type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'u':
                config.url = optarg;
                break;
            case 't': // --topic
                config.topic_name = optarg;
                break;
            case 'b':
                config.bitrate = atoi(optarg);
                break;
            case 'g':
                config.gop_size = atoi(optarg);
                break;
            case 1: // --hw-encoder
                config.use_hw_encoder = true;
                break;
            case 2: // --sw-encoder
                config.use_hw_encoder = false;
                break;
            case 'v':
                Logger::set_level(LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("appsrc", GST_LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("h264parse", GST_LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("rtmpsink", GST_LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("mpp", GST_LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("mppjpegdec", GST_LEVEL_DEBUG);
                gst_debug_set_threshold_for_name("mpph264enc", GST_LEVEL_DEBUG);
                break;
            case 3:
                print_usage(argv[0]);
                return 0;
            case 4: // --transport
                config.transport_type = optarg;
                break;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // 验证配置
    if (config.url.empty()) {
        std::cerr << "Stream URL is required" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    LOG_I("Starting cloud streamer service...");
    LOG_I("Type: %s, URL: %s", 
          (config.type == CloudStreamerConfig::WEBRTC) ? "WebRTC" : "RTMP",
          config.url.c_str());
    
    try {
        // 创建视频订阅者
        std::unique_ptr<video_transport::IVideoSubscriber> subscriber; // 视频订阅者
        video_transport::TransportConfig subscriber_config;
        subscriber_config.topic_name = config.topic_name;
        subscriber_config.domain_id = config.domain_id;
        subscriber_config.max_samples = config.max_samples;
        subscriber_config.timeout_ms = 1000;
        subscriber_config.ring_buffer_size = 3;

        if (config.transport_type == "DMA") {
            subscriber_config.type = video_transport::TransportType::DMA;
            subscriber = video_transport::VideoTransportFactory::create_subscriber(subscriber_config);
        } else if (config.transport_type == "SHMEM") {
            subscriber_config.type = video_transport::TransportType::SHMEM;
            subscriber = video_transport::VideoTransportFactory::create_subscriber(subscriber_config);
        } else if (config.transport_type == "FASTDDS") {
            subscriber_config.type = video_transport::TransportType::FASTDDS;
            subscriber = video_transport::VideoTransportFactory::create_subscriber(subscriber_config);
        } else {
            LOG_E("Invalid transport type: %s", config.transport_type.c_str());
            return 1;
        }

        // 创建并初始化服务
        g_cloud_streamer = std::make_unique<CloudStreamer>(config);
        if (!g_cloud_streamer->init(subscriber.get())) {
            LOG_E("Failed to initialize cloud streamer");
            return 1;
        }
        
        // 启动服务
        g_cloud_streamer->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            CloudStreamer::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            g_cloud_streamer->get_stats(stats);
            LOG_I("Stats - Sent: %lu, Dropped: %lu, FPS: %.1f, "
                  "Bitrate: %.1f kbps, CPU: %.1f%%",
                  stats.frames_sent, stats.frames_dropped, stats.fps,
                  stats.bitrate_kbps, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Cloud streamer service stopped");
    return 0;
}
